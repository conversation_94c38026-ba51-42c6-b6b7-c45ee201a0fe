#!/usr/bin/env python3
"""
BMN模型调试推理脚本
用于诊断BMN模型推理过程中的问题
"""

import os
import sys
import torch
import numpy as np
import json
from pathlib import Path

# 添加mmaction2路径
sys.path.insert(0, 'mmaction2')

from mmaction.utils import register_all_modules
from mmaction.apis import init_recognizer, inference_recognizer
from mmengine.config import Config

# 注册所有模块
register_all_modules()

def debug_model_output(config_path, checkpoint_path, feature_file):
    """
    调试单个特征文件的模型输出
    """
    print("=== BMN模型调试推理 ===")
    print(f"配置文件: {config_path}")
    print(f"检查点: {checkpoint_path}")
    print(f"特征文件: {feature_file}")
    
    # 加载模型
    model = init_recognizer(config_path, checkpoint_path, device='cuda' if torch.cuda.is_available() else 'cpu')
    print("✓ 模型加载成功")
    
    # 准备数据样本
    data_sample = {
        'feature_path': feature_file,
        'video_name': Path(feature_file).stem,
        'duration_second': 10.0,
        'duration_frame': 250,
        'feature_frame': 16  # 与BMN配置一致
    }
    
    print(f"数据样本: {data_sample}")
    
    # 进行推理
    print("\n开始推理...")
    result = inference_recognizer(model, data_sample)
    
    print(f"推理结果类型: {type(result)}")
    print(f"结果属性: {[attr for attr in dir(result) if not attr.startswith('_')]}")
    
    # 详细检查推理结果
    if hasattr(result, 'pred_instances'):
        pred_instances = result.pred_instances
        print(f"\n=== 预测实例详情 ===")
        print(f"预测实例类型: {type(pred_instances)}")
        print(f"预测实例属性: {[attr for attr in dir(pred_instances) if not attr.startswith('_')]}")
        
        # 检查各个属性
        if hasattr(pred_instances, 'segments'):
            segments = pred_instances.segments
            print(f"segments类型: {type(segments)}")
            print(f"segments形状: {segments.shape if hasattr(segments, 'shape') else 'N/A'}")
            print(f"segments内容: {segments}")
            
        if hasattr(pred_instances, 'scores'):
            scores = pred_instances.scores
            print(f"scores类型: {type(scores)}")
            print(f"scores形状: {scores.shape if hasattr(scores, 'shape') else 'N/A'}")
            print(f"scores内容: {scores}")
            print(f"scores范围: {scores.min().item() if len(scores) > 0 else 'N/A'} - {scores.max().item() if len(scores) > 0 else 'N/A'}")
            
        if hasattr(pred_instances, 'labels'):
            labels = pred_instances.labels
            print(f"labels类型: {type(labels)}")
            print(f"labels形状: {labels.shape if hasattr(labels, 'shape') else 'N/A'}")
            print(f"labels内容: {labels}")
    else:
        print("❌ 没有找到pred_instances属性")
    
    return result

def check_feature_file(feature_file):
    """
    检查特征文件的内容
    """
    print(f"\n=== 检查特征文件 ===")
    print(f"文件: {feature_file}")
    
    if not os.path.exists(feature_file):
        print("❌ 特征文件不存在")
        return
    
    # 读取特征文件
    import pandas as pd
    features = pd.read_csv(feature_file, header=None)
    
    print(f"特征形状: {features.shape}")
    print(f"特征数据类型: {features.dtypes.iloc[0]}")
    print(f"特征值范围: {features.min().min()} - {features.max().max()}")
    print(f"特征均值: {features.mean().mean():.4f}")
    print(f"特征标准差: {features.std().mean():.4f}")
    print(f"零值比例: {(features == 0).sum().sum() / (features.shape[0] * features.shape[1]):.2%}")
    
    # 检查前几行和后几行
    print(f"\n前5行特征统计:")
    for i in range(min(5, features.shape[0])):
        row = features.iloc[i]
        non_zero = (row != 0).sum()
        print(f"  行{i}: 非零值={non_zero}/{len(row)}, 均值={row.mean():.2f}, 标准差={row.std():.2f}")

def test_different_thresholds(config_path, checkpoint_path, feature_file):
    """
    测试不同阈值下的检测结果
    """
    print(f"\n=== 测试不同阈值 ===")
    
    # 加载配置
    cfg = Config.fromfile(config_path)
    
    # 测试不同的阈值
    thresholds = [0.01, 0.05, 0.1, 0.2, 0.3, 0.5]
    
    for threshold in thresholds:
        print(f"\n--- 测试阈值: {threshold} ---")
        
        # 修改配置中的阈值
        cfg.model.soft_nms_low_threshold = threshold
        cfg.model.soft_nms_high_threshold = min(threshold + 0.3, 0.9)
        
        # 保存临时配置
        temp_config = f"temp_config_thresh_{threshold}.py"
        cfg.dump(temp_config)
        
        try:
            # 使用新阈值进行推理
            model = init_recognizer(temp_config, checkpoint_path, device='cuda' if torch.cuda.is_available() else 'cpu')
            
            data_sample = {
                'feature_path': feature_file,
                'video_name': Path(feature_file).stem,
                'duration_second': 10.0,
                'duration_frame': 250,
                'feature_frame': 16
            }
            
            result = inference_recognizer(model, data_sample)
            
            if hasattr(result, 'pred_instances') and hasattr(result.pred_instances, 'scores'):
                scores = result.pred_instances.scores
                print(f"  检测数量: {len(scores)}")
                if len(scores) > 0:
                    print(f"  分数范围: {scores.min().item():.4f} - {scores.max().item():.4f}")
            else:
                print(f"  检测数量: 0")
                
        except Exception as e:
            print(f"  错误: {e}")
        finally:
            # 清理临时文件
            if os.path.exists(temp_config):
                os.remove(temp_config)

def main():
    # 配置参数
    config_path = "mmaction2/configs/localization/bmn/bmn_2xb4-16x400-20e_multiclass-tad-feature-debug.py"
    checkpoint_path = "work_dirs/test_bmn_multiclass_tad/best_auc_epoch_3.pth"
    feature_file = "real_tad_results_fixed/features/short_video_for_test_seg000.csv"
    
    # 检查文件是否存在
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        return
    
    if not os.path.exists(checkpoint_path):
        print(f"❌ 检查点文件不存在: {checkpoint_path}")
        return
    
    if not os.path.exists(feature_file):
        print(f"❌ 特征文件不存在: {feature_file}")
        return
    
    # 1. 检查特征文件
    check_feature_file(feature_file)
    
    # 2. 调试模型输出
    result = debug_model_output(config_path, checkpoint_path, feature_file)
    
    # 3. 测试不同阈值
    test_different_thresholds(config_path, checkpoint_path, feature_file)
    
    print("\n=== 调试完成 ===")

if __name__ == '__main__':
    main()
