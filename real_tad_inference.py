#!/usr/bin/env python3
"""
真正的TAD推理脚本
使用训练好的BMN模型对长视频进行时间动作检测，输出精确的动作边界和类别
"""

import os
import sys
import cv2
import torch
import numpy as np
import pandas as pd
import json
import argparse
from pathlib import Path
from tqdm import tqdm
import tempfile
from torch.utils.data import DataLoader

# 添加mmaction2路径
sys.path.insert(0, 'mmaction2')

from mmaction.utils import register_all_modules
from mmengine.config import Config
from mmengine.runner import Runner
from mmaction.registry import MODELS, DATASETS
from mmaction.apis import init_recognizer, inference_recognizer

# 注册所有模块
register_all_modules()

def extract_features_from_video(video_path, output_dir, segment_duration=10.0, overlap=2.0):
    """
    从长视频中提取特征，分段处理
    """
    print(f"正在处理视频: {video_path}")
    
    # 获取视频信息
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        raise ValueError(f"无法打开视频: {video_path}")
    
    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    duration = total_frames / fps
    
    print(f"视频信息: FPS={fps:.2f}, 总帧数={total_frames}, 时长={duration:.2f}秒")
    
    # 计算分段参数
    segment_frames = int(segment_duration * fps)
    overlap_frames = int(overlap * fps)
    step_frames = segment_frames - overlap_frames
    
    segments_info = []
    segment_idx = 0
    
    # 分段提取特征
    for start_frame in range(0, total_frames, step_frames):
        end_frame = min(start_frame + segment_frames, total_frames)
        
        if end_frame - start_frame < segment_frames // 2:
            break
        
        start_time = start_frame / fps
        end_time = end_frame / fps
        
        print(f"处理段 {segment_idx}: 帧 {start_frame}-{end_frame} (时间 {start_time:.2f}-{end_time:.2f}秒)")
        
        # 提取该段的帧
        frames = []
        cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
        
        for _ in range(end_frame - start_frame):
            ret, frame = cap.read()
            if not ret:
                break
            frames.append(frame)
        
        if len(frames) == 0:
            continue
        
        # 计算特征
        features = compute_segment_features(frames)
        
        # 保存特征文件
        video_name = Path(video_path).stem
        feature_filename = f"{video_name}_seg{segment_idx:03d}.csv"
        feature_path = os.path.join(output_dir, feature_filename)
        
        # 保存为CSV格式
        df = pd.DataFrame(features.T)  # 转置使得每行是一个时间步
        df.to_csv(feature_path, header=False, index=False)
        
        # 记录段信息
        segment_info = {
            'segment_id': segment_idx,
            'video_name': video_name,
            'feature_file': feature_filename,
            'start_time': start_time,
            'end_time': end_time,
            'start_frame': start_frame,
            'end_frame': end_frame,
            'duration': end_time - start_time,
            'feature_frame': features.shape[1]  # 时间步数
        }
        segments_info.append(segment_info)
        
        segment_idx += 1
    
    cap.release()
    print(f"共提取了 {len(segments_info)} 个段落的特征")
    
    return segments_info

def compute_segment_features(frames, target_dim=400):
    """计算视频段的特征"""
    if not frames:
        return np.zeros((target_dim, 1))
    
    features = []
    
    # 对每帧计算特征
    for frame in frames:
        # 计算颜色直方图特征
        hist_features = []
        
        # RGB直方图 (每个通道32个bin，共96维)
        for channel in range(3):
            hist = cv2.calcHist([frame], [channel], None, [32], [0, 256])
            hist_features.extend(hist.flatten())
        
        # 计算纹理特征 (简化的LBP特征，100维)
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        
        texture_features = [
            np.mean(grad_x), np.std(grad_x), np.mean(grad_y), np.std(grad_y),
            np.mean(np.abs(grad_x)), np.mean(np.abs(grad_y))
        ]
        
        # 扩展到100维
        texture_features.extend([0] * (100 - len(texture_features)))
        hist_features.extend(texture_features[:100])
        
        # 计算形状特征 (边缘密度等，100维)
        edges = cv2.Canny(gray, 50, 150)
        edge_density = np.sum(edges > 0) / (edges.shape[0] * edges.shape[1])
        
        shape_features = [edge_density]
        shape_features.extend([0] * (100 - len(shape_features)))
        hist_features.extend(shape_features[:100])
        
        # 其他特征填充到400维
        while len(hist_features) < target_dim:
            hist_features.append(0)
        
        features.append(hist_features[:target_dim])
    
    # 转换为numpy数组并转置
    features = np.array(features).T  # (400, num_frames)
    
    return features

def create_test_annotation(segments_info, output_path):
    """为推理创建测试标注文件"""
    annotations = {}
    
    for segment in segments_info:
        video_key = segment['feature_file'].replace('.csv', '')
        
        annotations[video_key] = {
            'duration_second': segment['duration'],
            'duration_frame': segment['feature_frame'],
            'feature_frame': segment['feature_frame'],
            'annotations': []  # 测试时不需要真实标注
        }
    
    with open(output_path, 'w') as f:
        json.dump(annotations, f, indent=2)
    
    print(f"测试标注文件已保存到: {output_path}")
    return annotations

def run_bmn_inference_simple(config_path, checkpoint_path, feature_dir, annotation_path, output_dir):
    """
    简化的BMN模型推理，直接加载模型进行推理
    """
    print("开始BMN推理...")

    # 加载配置
    cfg = Config.fromfile(config_path)

    # 构建模型
    model = MODELS.build(cfg.model)

    # 加载检查点
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    if 'state_dict' in checkpoint:
        model.load_state_dict(checkpoint['state_dict'])
    else:
        model.load_state_dict(checkpoint)

    # 设置为评估模式
    model.eval()

    # 如果有GPU，移动到GPU
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    print(f"模型已加载到设备: {device}")

    # 构建测试数据集
    test_dataset = DATASETS.build(dict(
        type='ActivityNetDataset',
        ann_file=annotation_path,
        data_prefix=dict(video=feature_dir + '/'),
        pipeline=[
            dict(type='LoadLocalizationFeature'),
            dict(type='ResizeLocalizationFeature', temporal_dim=16, interpolation_mode='linear'),
            dict(
                type='PackLocalizationInputs',
                keys=('gt_bbox', ),
                meta_keys=('video_name', 'duration_second', 'duration_frame',
                           'annotations', 'feature_frame'))
        ],
        test_mode=True
    ))

    # 创建数据加载器
    test_dataloader = DataLoader(
        test_dataset,
        batch_size=1,
        num_workers=0,  # 设为0避免多进程问题
        shuffle=False,
        drop_last=False
    )

    print(f"创建了包含 {len(test_dataset)} 个样本的测试数据集")

    # 手动运行推理循环
    results = []

    print("开始推理...")
    for idx, data_batch in enumerate(tqdm(test_dataloader, desc="推理进度")):
        # 将数据移到正确的设备
        for key in data_batch:
            if isinstance(data_batch[key], torch.Tensor):
                data_batch[key] = data_batch[key].to(device)
            elif isinstance(data_batch[key], dict):
                for sub_key in data_batch[key]:
                    if isinstance(data_batch[key][sub_key], torch.Tensor):
                        data_batch[key][sub_key] = data_batch[key][sub_key].to(device)

        # 运行模型推理
        with torch.no_grad():
            outputs = model.test_step(data_batch)

        results.extend(outputs)

        if (idx + 1) % 10 == 0:
            print(f"已处理 {idx + 1}/{len(test_dataloader)} 个样本")

    # 手动保存推理结果
    results_file = os.path.join(output_dir, 'bmn_results.json')
    save_inference_results(results, results_file)

    print(f"BMN推理完成，结果保存在: {output_dir}")
    return results


def run_bmn_inference_api(config_path, checkpoint_path, feature_dir, annotation_path, output_dir):
    """
    使用MMAction2 API进行BMN推理（推荐方法）
    """
    print("使用MMAction2 API进行BMN推理...")

    # 加载模型
    model = init_recognizer(config_path, checkpoint_path, device='cuda' if torch.cuda.is_available() else 'cpu')
    print("✓ BMN模型加载成功")

    # 读取标注文件获取特征文件列表
    with open(annotation_path, 'r') as f:
        annotations = json.load(f)

    results = []
    print(f"开始推理 {len(annotations)} 个特征文件...")

    for video_name, ann_info in tqdm(annotations.items(), desc="推理进度"):
        feature_file = os.path.join(feature_dir, f"{video_name}.csv")

        if not os.path.exists(feature_file):
            print(f"警告: 特征文件不存在 {feature_file}")
            continue

        # 准备数据样本
        data_sample = {
            'feature_path': feature_file,
            'video_name': video_name,
            'duration_second': ann_info.get('duration_second', 10.0),
            'duration_frame': ann_info.get('duration_frame', 250),
            'feature_frame': ann_info.get('feature_frame', 16)
        }

        try:
            # 进行推理
            result = inference_recognizer(model, data_sample)

            # 处理结果
            result_data = {
                'video_name': video_name,
                'pred_instances': result.pred_instances if hasattr(result, 'pred_instances') else None
            }
            results.append(result_data)

        except Exception as e:
            print(f"推理失败 {video_name}: {e}")
            results.append({'video_name': video_name, 'pred_instances': None})

    # 保存推理结果
    results_file = os.path.join(output_dir, 'bmn_results.json')
    save_inference_results(results, results_file)

    print(f"BMN推理完成，结果保存在: {output_dir}")
    return results


def run_bmn_inference(config_path, checkpoint_path, feature_dir, annotation_path, output_dir):
    """
    运行BMN模型推理，获取真正的TAD结果
    """
    try:
        # 首先尝试MMAction2 API方法（推荐）
        return run_bmn_inference_api(config_path, checkpoint_path, feature_dir, annotation_path, output_dir)
    except Exception as e:
        print(f"API推理方法失败: {e}")
        print("尝试简化推理方法...")

        try:
            # 备用方法1：简化推理
            return run_bmn_inference_simple(config_path, checkpoint_path, feature_dir, annotation_path, output_dir)
        except Exception as e2:
            print(f"简化推理方法也失败: {e2}")
            print("尝试Runner方法...")

            # 备用方法2：使用Runner
            return run_bmn_inference_with_runner(config_path, checkpoint_path, feature_dir, annotation_path, output_dir)


def run_bmn_inference_with_runner(config_path, checkpoint_path, feature_dir, annotation_path, output_dir):
    """
    使用MMEngine Runner进行BMN推理的备用方法
    """
    print("使用Runner方法进行BMN推理...")

    # 加载配置
    cfg = Config.fromfile(config_path)

    # 修改配置以使用我们的数据
    cfg.test_dataloader.dataset.ann_file = annotation_path
    cfg.test_dataloader.dataset.data_prefix.video = feature_dir + '/'
    cfg.test_dataloader.batch_size = 1

    # 设置工作目录和检查点
    cfg.work_dir = output_dir
    cfg.load_from = checkpoint_path

    # 保持test_cfg，但创建一个简单的evaluator
    # 这样满足MMEngine的要求：要么全部None，要么全部不None
    cfg.test_evaluator = dict(
        type='DummyMetric'  # 使用虚拟评估器
    )

    # 创建runner并运行推理
    runner = Runner.from_cfg(cfg)

    # 运行测试
    runner.test()

    print(f"BMN推理完成，结果保存在: {output_dir}")
    return []

def save_inference_results(results, output_file):
    """
    保存推理结果到JSON文件
    """
    # 处理推理结果
    processed_results = {}

    for result in results:
        # 获取视频名称
        video_name = result.get('video_name', 'unknown')

        # 获取预测结果
        pred_instances = result.get('pred_instances', None)
        detections = []

        if pred_instances is not None:
            # 提取检测结果
            try:
                if hasattr(pred_instances, 'segments') and hasattr(pred_instances, 'scores'):
                    segments = pred_instances.segments
                    scores = pred_instances.scores
                    labels = pred_instances.labels if hasattr(pred_instances, 'labels') else None

                    # 转换为numpy数组（如果是tensor）
                    if hasattr(segments, 'cpu'):
                        segments = segments.cpu().numpy()
                    if hasattr(scores, 'cpu'):
                        scores = scores.cpu().numpy()
                    if labels is not None and hasattr(labels, 'cpu'):
                        labels = labels.cpu().numpy()

                    for i in range(len(segments)):
                        detection = {
                            'segment': segments[i].tolist() if hasattr(segments[i], 'tolist') else list(segments[i]),
                            'score': float(scores[i]),
                            'label': int(labels[i]) if labels is not None else 0
                        }
                        detections.append(detection)
            except Exception as e:
                print(f"处理 {video_name} 的预测结果时出错: {e}")

        processed_results[video_name] = detections

    # 保存到文件
    with open(output_file, 'w') as f:
        json.dump(processed_results, f, indent=2)

    print(f"推理结果已保存到: {output_file}")
    print(f"总共处理了 {len(processed_results)} 个视频段落")

    # 打印检测统计
    total_detections = sum(len(dets) for dets in processed_results.values())
    print(f"总检测数: {total_detections}")

def parse_bmn_results(results_dir, segments_info, class_names):
    """
    解析BMN推理结果，提取动作检测信息
    """
    # 查找BMN输出的结果文件
    results_file = os.path.join(results_dir, 'bmn_results.json')
    
    if not os.path.exists(results_file):
        print(f"未找到BMN结果文件: {results_file}")
        return None
    
    print(f"解析BMN结果文件: {results_file}")
    
    # 读取BMN结果
    with open(results_file, 'r') as f:
        bmn_results = json.load(f)
    
    # 解析每个段落的检测结果
    all_detections = []
    
    for segment in segments_info:
        segment_key = segment['feature_file'].replace('.csv', '')
        
        if segment_key in bmn_results:
            segment_detections = bmn_results[segment_key]
            
            # 将段落内的时间转换为全局时间
            for detection in segment_detections:
                global_detection = {
                    'segment_id': segment['segment_id'],
                    'local_start': detection['segment'][0],
                    'local_end': detection['segment'][1],
                    'global_start': segment['start_time'] + detection['segment'][0],
                    'global_end': segment['start_time'] + detection['segment'][1],
                    'score': detection.get('score', 0.0),
                    'label': detection.get('label', 'unknown'),
                    'label_name': class_names.get(detection.get('label', 0), 'unknown')
                }
                all_detections.append(global_detection)
    
    return all_detections

def merge_overlapping_detections(detections, overlap_threshold=0.5):
    """
    合并重叠的检测结果
    """
    if not detections:
        return []
    
    # 按开始时间排序
    detections.sort(key=lambda x: x['global_start'])
    
    merged = []
    current = detections[0].copy()
    
    for next_det in detections[1:]:
        # 计算重叠度
        overlap_start = max(current['global_end'], next_det['global_start'])
        overlap_end = min(current['global_end'], next_det['global_end'])
        overlap_duration = max(0, overlap_end - overlap_start)
        
        current_duration = current['global_end'] - current['global_start']
        next_duration = next_det['global_end'] - next_det['global_start']
        
        overlap_ratio = overlap_duration / min(current_duration, next_duration)
        
        if overlap_ratio > overlap_threshold and current['label_name'] == next_det['label_name']:
            # 合并检测结果
            current['global_end'] = max(current['global_end'], next_det['global_end'])
            current['score'] = max(current['score'], next_det['score'])
        else:
            # 保存当前检测，开始新的检测
            merged.append(current)
            current = next_det.copy()
    
    merged.append(current)
    return merged

def extract_action_clips(video_path, detections, output_dir, min_score=0.3, target_actions=None):
    """
    从长视频中精确截取动作片段并保存

    Args:
        video_path: 原始视频路径
        detections: 检测结果列表
        output_dir: 输出目录
        min_score: 最小置信度阈值
        target_actions: 感兴趣的动作类别列表，None表示所有动作
    """
    print("\n=== 开始截取动作片段 ===")

    # 默认感兴趣的动作（排除'ok'类别，专注于缺陷检测）
    if target_actions is None:
        target_actions = ['nok_appearance_defect', 'nok_electric_defect']

    # 创建动作片段输出目录
    clips_dir = Path(output_dir) / 'action_clips'
    clips_dir.mkdir(exist_ok=True)

    # 为每个动作类别创建子目录
    for action in target_actions:
        action_dir = clips_dir / action
        action_dir.mkdir(exist_ok=True)

    # 打开原始视频
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"无法打开视频: {video_path}")
        return []

    fps = cap.get(cv2.CAP_PROP_FPS)
    video_name = Path(video_path).stem

    extracted_clips = []

    # 筛选感兴趣的高置信度检测结果
    filtered_detections = [
        det for det in detections
        if det['score'] >= min_score and det['label_name'] in target_actions
    ]

    print(f"找到 {len(filtered_detections)} 个感兴趣的动作片段（置信度 >= {min_score}）")

    for i, detection in enumerate(filtered_detections):
        start_time = detection['global_start']
        end_time = detection['global_end']
        action_name = detection['label_name']
        score = detection['score']

        print(f"截取片段 {i+1}: {action_name} ({start_time:.2f}s - {end_time:.2f}s) 置信度: {score:.3f}")

        # 计算帧范围（添加小的缓冲区）
        buffer_time = 0.5  # 前后各加0.5秒缓冲
        start_frame = max(0, int((start_time - buffer_time) * fps))
        end_frame = int((end_time + buffer_time) * fps)

        # 设置输出文件名
        clip_filename = f"{video_name}_{action_name}_{i+1:03d}_{start_time:.1f}s-{end_time:.1f}s_score{score:.2f}.mp4"
        clip_path = clips_dir / action_name / clip_filename

        # 截取视频片段
        success = extract_video_segment(cap, start_frame, end_frame, str(clip_path), fps)

        if success:
            clip_info = {
                'clip_id': i + 1,
                'action_name': action_name,
                'start_time': start_time,
                'end_time': end_time,
                'duration': end_time - start_time,
                'score': score,
                'clip_path': str(clip_path),
                'clip_filename': clip_filename
            }
            extracted_clips.append(clip_info)
            print(f"  ✓ 成功保存: {clip_filename}")
        else:
            print(f"  ✗ 截取失败: {clip_filename}")

    cap.release()

    print(f"\n成功截取 {len(extracted_clips)} 个动作片段")
    return extracted_clips

def extract_video_segment(cap, start_frame, end_frame, output_path, fps):
    """
    从视频中截取指定帧范围的片段
    """
    try:
        # 设置视频编码器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')

        # 获取视频尺寸
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

        # 创建视频写入器
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

        # 跳转到开始帧
        cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)

        # 读取并写入帧
        for _ in range(end_frame - start_frame):
            ret, frame = cap.read()
            if not ret:
                break
            out.write(frame)

        out.release()
        return True

    except Exception as e:
        print(f"截取视频片段时出错: {e}")
        return False

def save_final_results(detections, segments_info, output_path, video_path, extracted_clips=None):
    """
    保存最终的TAD结果，包括截取的动作片段信息
    """
    video_name = Path(video_path).stem

    final_results = {
        'video_info': {
            'video_name': video_name,
            'video_path': str(video_path),
            'total_segments': len(segments_info),
            'total_detections': len(detections),
            'extracted_clips': len(extracted_clips) if extracted_clips else 0
        },
        'detections': detections,
        'extracted_clips': extracted_clips or [],
        'segments_info': segments_info
    }

    with open(output_path, 'w') as f:
        json.dump(final_results, f, indent=2, ensure_ascii=False)

    print(f"最终TAD结果已保存到: {output_path}")

    # 打印检测摘要
    print("\n=== TAD检测结果摘要 ===")
    print(f"视频: {video_name}")
    print(f"总检测数: {len(detections)}")
    print(f"截取片段数: {len(extracted_clips) if extracted_clips else 0}")

    for i, det in enumerate(detections):
        print(f"检测 {i+1}: {det['label_name']} "
              f"({det['global_start']:.2f}s - {det['global_end']:.2f}s) "
              f"置信度: {det['score']:.3f}")

    # 打印截取片段摘要
    if extracted_clips:
        print("\n=== 截取的动作片段 ===")
        for clip in extracted_clips:
            print(f"片段 {clip['clip_id']}: {clip['action_name']} "
                  f"({clip['start_time']:.2f}s - {clip['end_time']:.2f}s) "
                  f"置信度: {clip['score']:.3f}")
            print(f"  保存路径: {clip['clip_path']}")

def main():
    parser = argparse.ArgumentParser(description='真正的长视频TAD推理')
    parser.add_argument('video_path', help='输入视频路径')
    parser.add_argument('config', help='BMN模型配置文件路径')
    parser.add_argument('checkpoint', help='BMN模型检查点路径')
    parser.add_argument('--output-dir', default='./real_tad_results', help='输出目录')
    parser.add_argument('--segment-duration', type=float, default=10.0, help='分段时长（秒）')
    parser.add_argument('--overlap', type=float, default=2.0, help='重叠时长（秒）')
    parser.add_argument('--min-score', type=float, default=0.3, help='动作检测最小置信度阈值')
    parser.add_argument('--target-actions', nargs='+',
                       default=['nok_appearance_defect', 'nok_electric_defect'],
                       help='感兴趣的动作类别列表')
    
    args = parser.parse_args()
    
    # 类别名称映射
    class_names = {
        0: 'nok_appearance_defect',
        1: 'nok_electric_defect', 
        2: 'ok'
    }
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    feature_dir = output_dir / 'features'
    feature_dir.mkdir(exist_ok=True)
    
    # 步骤1: 提取特征
    print("=== 步骤1: 提取视频特征 ===")
    segments_info = extract_features_from_video(
        args.video_path, 
        str(feature_dir),
        args.segment_duration,
        args.overlap
    )
    
    # 步骤2: 创建测试标注文件
    print("=== 步骤2: 创建测试标注文件 ===")
    annotation_path = output_dir / 'test_annotations.json'
    create_test_annotation(segments_info, str(annotation_path))
    
    # 步骤3: 运行BMN推理
    print("=== 步骤3: 运行BMN推理 ===")
    inference_output_dir = output_dir / 'bmn_inference'
    inference_output_dir.mkdir(exist_ok=True)
    
    run_bmn_inference(
        args.config,
        args.checkpoint,
        str(feature_dir),
        str(annotation_path),
        str(inference_output_dir)
    )
    
    # 步骤4: 解析BMN结果
    print("=== 步骤4: 解析BMN推理结果 ===")
    detections = parse_bmn_results(str(inference_output_dir), segments_info, class_names)
    
    if detections:
        # 步骤5: 合并重叠检测
        print("=== 步骤5: 合并重叠检测 ===")
        merged_detections = merge_overlapping_detections(detections)

        # 步骤6: 截取感兴趣的动作片段
        print("=== 步骤6: 截取动作片段 ===")
        extracted_clips = extract_action_clips(
            args.video_path,
            merged_detections,
            str(output_dir),
            min_score=args.min_score,
            target_actions=args.target_actions
        )

        # 步骤7: 保存最终结果
        print("=== 步骤7: 保存最终结果 ===")
        final_results_path = output_dir / 'tad_results.json'
        save_final_results(merged_detections, segments_info, str(final_results_path), args.video_path, extracted_clips)
    else:
        print("未检测到任何动作")
    
    print(f"\n=== TAD推理完成 ===")

if __name__ == '__main__':
    main()
