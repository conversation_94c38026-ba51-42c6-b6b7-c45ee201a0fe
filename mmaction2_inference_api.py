#!/usr/bin/env python3
"""
使用MMAction2推理API调用训练好的BMN模型
"""

import os
import sys
import numpy as np
import pandas as pd
import json
from pathlib import Path

# 添加mmaction2路径
sys.path.insert(0, 'mmaction2')

from mmaction.apis import init_recognizer, inference_recognizer
from mmaction.utils import register_all_modules
from mmengine.config import Config

# 注册所有模块
register_all_modules()

def load_bmn_model(config_path, checkpoint_path, device='cuda:0'):
    """
    加载训练好的BMN模型
    """
    print(f"加载BMN模型...")
    print(f"配置文件: {config_path}")
    print(f"检查点: {checkpoint_path}")
    
    # 使用init_recognizer加载模型
    model = init_recognizer(config_path, checkpoint_path, device=device)
    
    print("✓ BMN模型加载成功")
    return model

def prepare_feature_data(feature_file):
    """
    准备特征数据用于推理
    """
    # 创建数据样本，使用feature_path让LoadLocalizationFeature自动加载
    data_sample = {
        'feature_path': str(feature_file),
        'video_name': Path(feature_file).stem,
        'duration_second': 10.0,  # 段落时长
        'duration_frame': 250,    # 假设的帧数
        'feature_frame': 16       # resize后的时间步数
    }

    return data_sample

def inference_single_feature(model, feature_file):
    """
    对单个特征文件进行推理
    """
    print(f"推理特征文件: {feature_file}")
    
    # 准备数据
    data_sample = prepare_feature_data(feature_file)
    
    # 进行推理
    result = inference_recognizer(model, data_sample)
    
    return result

def inference_feature_directory(model, feature_dir, output_file=None):
    """
    对整个特征目录进行推理
    """
    feature_dir = Path(feature_dir)
    feature_files = list(feature_dir.glob('*.csv'))
    
    print(f"找到 {len(feature_files)} 个特征文件")
    
    all_results = {}
    
    for feature_file in feature_files:
        try:
            result = inference_single_feature(model, str(feature_file))
            
            # 提取关键信息
            video_name = feature_file.stem
            
            # 处理推理结果
            if hasattr(result, 'pred_instances'):
                pred_instances = result.pred_instances
                
                detections = []
                if hasattr(pred_instances, 'segments') and hasattr(pred_instances, 'scores'):
                    segments = pred_instances.segments.cpu().numpy()
                    scores = pred_instances.scores.cpu().numpy()
                    labels = pred_instances.labels.cpu().numpy() if hasattr(pred_instances, 'labels') else None
                    
                    for i in range(len(segments)):
                        detection = {
                            'segment': segments[i].tolist(),
                            'score': float(scores[i]),
                            'label': int(labels[i]) if labels is not None else 0
                        }
                        detections.append(detection)
                
                all_results[video_name] = detections
                print(f"  ✓ {video_name}: 检测到 {len(detections)} 个动作")
            else:
                print(f"  ✗ {video_name}: 无推理结果")
                all_results[video_name] = []
                
        except Exception as e:
            print(f"  ✗ {feature_file}: 推理失败 - {e}")
            all_results[feature_file.stem] = []
    
    # 保存结果
    if output_file:
        with open(output_file, 'w') as f:
            json.dump(all_results, f, indent=2)
        print(f"推理结果已保存到: {output_file}")
    
    return all_results

def demo_inference():
    """
    演示如何使用推理API
    """
    # 模型路径
    config_path = "mmaction2/configs/localization/bmn/bmn_2xb4-16x400-20e_multiclass-tad-feature.py"
    checkpoint_path = "work_dirs/test_bmn_multiclass_tad/best_auc_epoch_3.pth"
    
    # 特征目录
    feature_dir = "long_video_inference_results/features"
    output_file = "api_inference_results.json"
    
    print("=== MMAction2推理API演示 ===")
    
    # 1. 加载模型
    model = load_bmn_model(config_path, checkpoint_path)
    
    # 2. 批量推理
    results = inference_feature_directory(model, feature_dir, output_file)
    
    # 3. 打印摘要
    print(f"\n=== 推理摘要 ===")
    total_detections = 0
    for video_name, detections in results.items():
        print(f"{video_name}: {len(detections)} 个检测")
        total_detections += len(detections)
    
    print(f"总计: {total_detections} 个动作检测")

def inference_with_custom_data(config_path, checkpoint_path, feature_data):
    """
    使用自定义数据进行推理
    
    Args:
        config_path: 配置文件路径
        checkpoint_path: 检查点路径
        feature_data: 特征数据 (feat_dim, temporal_dim)
    """
    # 加载模型
    model = load_bmn_model(config_path, checkpoint_path)
    
    # 准备数据样本
    data_sample = {
        'raw_feature': feature_data,
        'video_name': 'custom_video',
        'duration_second': feature_data.shape[1] / 16.0,
        'duration_frame': feature_data.shape[1],
        'feature_frame': feature_data.shape[1]
    }
    
    # 进行推理
    result = inference_recognizer(model, data_sample)
    
    return result

def test_single_file_inference():
    """
    测试单个文件推理
    """
    config_path = "mmaction2/configs/localization/bmn/bmn_2xb4-16x400-20e_multiclass-tad-feature.py"
    checkpoint_path = "work_dirs/test_bmn_multiclass_tad/best_auc_epoch_3.pth"
    
    # 选择一个特征文件进行测试
    feature_file = "long_video_inference_results/features/20250728T075557Z_20250728T080057Z_decrypted_roi_seg000.csv"
    
    if not os.path.exists(feature_file):
        print(f"特征文件不存在: {feature_file}")
        return
    
    print("=== 单文件推理测试 ===")
    
    # 加载模型
    model = load_bmn_model(config_path, checkpoint_path)
    
    # 推理单个文件
    result = inference_single_feature(model, feature_file)
    
    # 打印结果
    print(f"推理结果类型: {type(result)}")
    print(f"结果属性: {dir(result)}")
    
    if hasattr(result, 'pred_instances'):
        pred_instances = result.pred_instances
        print(f"预测实例: {pred_instances}")
        
        if hasattr(pred_instances, 'segments'):
            segments = pred_instances.segments
            print(f"检测段落: {segments}")
        
        if hasattr(pred_instances, 'scores'):
            scores = pred_instances.scores
            print(f"置信度分数: {scores}")
        
        if hasattr(pred_instances, 'labels'):
            labels = pred_instances.labels
            print(f"标签: {labels}")

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='MMAction2推理API演示')
    parser.add_argument('--mode', choices=['demo', 'single'], default='demo',
                       help='运行模式: demo(批量推理) 或 single(单文件测试)')
    
    args = parser.parse_args()
    
    if args.mode == 'demo':
        demo_inference()
    elif args.mode == 'single':
        test_single_file_inference()
